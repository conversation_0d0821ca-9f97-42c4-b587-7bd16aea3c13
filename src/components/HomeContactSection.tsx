'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Mail, 
  Phone, 
  MessageCircle, 
  ArrowRight,
  MapPin,
  Clock
} from 'lucide-react';

const contactMethods = [
  {
    icon: Mail,
    title: 'Email Us',
    description: 'Get in touch via email',
    contact: '<EMAIL>',
    action: 'Send Email',
    href: 'mailto:<EMAIL>',
    color: 'text-blue-600 bg-blue-100'
  },
  {
    icon: Phone,
    title: 'Call Us',
    description: 'Speak with our team',
    contact: '+****************',
    action: 'Call Now',
    href: 'tel:+15551234567',
    color: 'text-green-600 bg-green-100'
  },
  {
    icon: MessageCircle,
    title: 'Live Chat',
    description: 'Chat with support',
    contact: 'Coming Soon',
    action: 'Coming Soon',
    href: '#',
    color: 'text-purple-600 bg-purple-100'
  }
];

export const HomeContactSection = () => {
  return (
    <section className="py-12 sm:py-16 lg:py-20 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4 sm:mb-6">
            Need Help? We're Here for You
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto">
            Our dedicated support team is ready to assist you with any questions about our templates, 
            customization services, or technical support.
          </p>
        </div>

        {/* Contact Methods Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 mb-12 lg:mb-16">
          {contactMethods.map((method, index) => {
            const IconComponent = method.icon;
            return (
              <Card key={index} className="group hover:shadow-lg transition-all duration-300">
                <CardContent className="p-6 lg:p-8 text-center">
                  <div className={`w-16 h-16 lg:w-20 lg:h-20 rounded-full ${method.color} flex items-center justify-center mx-auto mb-4 lg:mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className="h-8 w-8 lg:h-10 lg:w-10" />
                  </div>
                  <h3 className="text-lg lg:text-xl font-semibold text-gray-900 mb-2 lg:mb-3">
                    {method.title}
                  </h3>
                  <p className="text-sm lg:text-base text-gray-600 mb-3 lg:mb-4">
                    {method.description}
                  </p>
                  <p className="text-sm lg:text-base font-medium text-gray-900 mb-4 lg:mb-6">
                    {method.contact}
                  </p>
                  <Button asChild variant="outline" size="sm" className="w-full sm:w-auto">
                    <Link href={method.href}>
                      {method.action}
                      <ArrowRight className="ml-2 h-3 w-3" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Business Hours & Location */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 mb-12 lg:mb-16">
          {/* Business Hours */}
          <Card>
            <CardContent className="p-6 lg:p-8">
              <div className="flex items-start space-x-4">
                <div className="p-3 bg-purple-100 rounded-lg flex-shrink-0">
                  <Clock className="h-6 w-6 text-purple-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg lg:text-xl font-semibold text-gray-900 mb-4">Business Hours</h3>
                  <div className="space-y-2 text-sm lg:text-base">
                    <div className="flex flex-col sm:flex-row sm:justify-between">
                      <span className="text-gray-600">Monday - Friday</span>
                      <span className="font-medium text-gray-900">9:00 AM - 6:00 PM EST</span>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:justify-between">
                      <span className="text-gray-600">Saturday</span>
                      <span className="font-medium text-gray-900">10:00 AM - 4:00 PM EST</span>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:justify-between">
                      <span className="text-gray-600">Sunday</span>
                      <span className="font-medium text-red-600">Closed</span>
                    </div>
                  </div>
                  <p className="text-xs lg:text-sm text-blue-600 mt-4 font-medium">
                    📧 Email support available 24/7
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Office Location */}
          <Card>
            <CardContent className="p-6 lg:p-8">
              <div className="flex items-start space-x-4">
                <div className="p-3 bg-green-100 rounded-lg flex-shrink-0">
                  <MapPin className="h-6 w-6 text-green-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg lg:text-xl font-semibold text-gray-900 mb-4">Office Location</h3>
                  <p className="text-sm lg:text-base text-gray-600 mb-4">
                    123 Design Street<br />
                    Creative District<br />
                    San Francisco, CA 94102<br />
                    United States
                  </p>
                  <Button asChild variant="outline" size="sm">
                    <Link href="https://maps.google.com" target="_blank">
                      Get Directions
                      <ArrowRight className="ml-2 h-3 w-3" />
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* CTA Buttons */}
        <div className="text-center">
          <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
            <Button asChild size="lg" className="flex-1">
              <Link href="/contact">
                Contact Us
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="flex-1">
              <Link href="/custom-request">
                Custom Design
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};
