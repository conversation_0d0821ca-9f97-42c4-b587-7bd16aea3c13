'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Mail, 
  Phone, 
  MessageCircle, 
  ArrowRight,
  MapPin,
  Clock
} from 'lucide-react';

const contactMethods = [
  {
    icon: Mail,
    title: 'Email Us',
    description: 'Get in touch via email',
    contact: '<EMAIL>',
    action: 'Send Email',
    href: 'mailto:<EMAIL>',
    color: 'text-blue-600 bg-blue-100'
  },
  {
    icon: Phone,
    title: 'Call Us',
    description: 'Speak with our team',
    contact: '+****************',
    action: 'Call Now',
    href: 'tel:+15551234567',
    color: 'text-green-600 bg-green-100'
  },
  {
    icon: MessageCircle,
    title: 'Live Chat',
    description: 'Chat with support',
    contact: 'Available 24/7',
    action: 'Start Chat',
    href: '/contact',
    color: 'text-purple-600 bg-purple-100'
  }
];

export const ContactSection = () => {
  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div>
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Need Help? We're Here for You
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 mb-8 leading-relaxed">
              Our dedicated support team is ready to assist you with any questions about our templates, 
              customization services, or technical support. Get in touch today!
            </p>
            
            {/* Contact Methods */}
            <div className="space-y-4 mb-8">
              {contactMethods.map((method, index) => {
                const IconComponent = method.icon;
                return (
                  <div key={index} className="flex items-center space-x-4">
                    <div className={`w-12 h-12 rounded-lg ${method.color} flex items-center justify-center`}>
                      <IconComponent className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{method.title}</h3>
                      <p className="text-gray-600">{method.contact}</p>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Business Hours */}
            <div className="bg-gray-50 rounded-lg p-6 mb-8">
              <div className="flex items-center space-x-3 mb-3">
                <Clock className="h-5 w-5 text-gray-600" />
                <h3 className="font-semibold text-gray-900">Business Hours</h3>
              </div>
              <div className="space-y-1 text-gray-600">
                <p>Monday - Friday: 9:00 AM - 6:00 PM EST</p>
                <p>Saturday: 10:00 AM - 4:00 PM EST</p>
                <p>Sunday: Closed</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button asChild size="lg" className="flex-1">
                <Link href="/contact">
                  Contact Us
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="flex-1">
                <Link href="/custom-request">
                  Request Custom Design
                </Link>
              </Button>
            </div>
          </div>

          {/* Right Content - Contact Cards */}
          <div className="space-y-6">
            {contactMethods.map((method, index) => {
              const IconComponent = method.icon;
              return (
                <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className={`w-14 h-14 rounded-xl ${method.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                        <IconComponent className="h-7 w-7" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                          {method.title}
                        </h3>
                        <p className="text-gray-600 mb-3">
                          {method.description}
                        </p>
                        <p className="text-sm font-medium text-gray-900 mb-4">
                          {method.contact}
                        </p>
                        <Button asChild variant="outline" size="sm">
                          <Link href={method.href}>
                            {method.action}
                            <ArrowRight className="ml-2 h-3 w-3" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}

            {/* Location Card */}
            <Card className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="w-14 h-14 rounded-xl text-orange-600 bg-orange-100 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <MapPin className="h-7 w-7" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      Visit Our Office
                    </h3>
                    <p className="text-gray-600 mb-3">
                      Come meet our team in person
                    </p>
                    <p className="text-sm font-medium text-gray-900 mb-4">
                      123 Business Ave, Suite 100<br />
                      New York, NY 10001
                    </p>
                    <Button asChild variant="outline" size="sm">
                      <Link href="https://maps.google.com" target="_blank">
                        Get Directions
                        <ArrowRight className="ml-2 h-3 w-3" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};
