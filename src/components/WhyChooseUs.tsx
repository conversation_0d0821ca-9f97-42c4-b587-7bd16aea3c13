'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Zap, 
  Shield, 
  Headphones, 
  Award, 
  Clock, 
  Users,
  CheckCircle,
  Star
} from 'lucide-react';

const features = [
  {
    icon: Zap,
    title: 'Lightning Fast',
    description: 'Get your templates instantly with our optimized delivery system',
    color: 'text-yellow-600 bg-yellow-100'
  },
  {
    icon: Shield,
    title: 'Secure & Reliable',
    description: 'All templates are tested and come with security best practices',
    color: 'text-green-600 bg-green-100'
  },
  {
    icon: Headphones,
    title: '24/7 Support',
    description: 'Our expert team is always ready to help you succeed',
    color: 'text-blue-600 bg-blue-100'
  },
  {
    icon: Award,
    title: 'Premium Quality',
    description: 'Hand-crafted templates by professional designers and developers',
    color: 'text-purple-600 bg-purple-100'
  }
];

const stats = [
  {
    icon: Users,
    number: '10,000+',
    label: 'Happy Customers',
    color: 'text-blue-600'
  },
  {
    icon: CheckCircle,
    number: '500+',
    label: 'Templates Available',
    color: 'text-green-600'
  },
  {
    icon: Star,
    number: '4.9/5',
    label: 'Average Rating',
    color: 'text-yellow-600'
  },
  {
    icon: Clock,
    number: '99.9%',
    label: 'Uptime Guarantee',
    color: 'text-purple-600'
  }
];

export const WhyChooseUs = () => {
  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            Why Choose KaleidoneX
          </Badge>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Built for Modern Businesses
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
            We provide more than just templates. We deliver complete solutions that help your business grow and succeed in the digital world.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                <CardContent className="p-8 text-center">
                  <div className={`w-16 h-16 rounded-full ${feature.color} flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Stats Section */}
        <div className="bg-white rounded-2xl shadow-xl p-8 sm:p-12">
          <div className="text-center mb-12">
            <h3 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
              Trusted by Thousands
            </h3>
            <p className="text-gray-600 text-lg">
              Join our growing community of successful businesses
            </p>
          </div>
          
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div key={index} className="text-center">
                  <div className="flex justify-center mb-4">
                    <IconComponent className={`h-8 w-8 ${stat.color}`} />
                  </div>
                  <div className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">
                    {stat.number}
                  </div>
                  <div className="text-gray-600 font-medium">
                    {stat.label}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};
